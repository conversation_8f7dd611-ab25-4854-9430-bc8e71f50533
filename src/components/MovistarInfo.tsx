import React, { useState, useEffect } from "react";
import ReactMarkdown from "react-markdown";
import { MovistarOttService } from "../services/MovistarOttService";
import { IconButton, Image, LoadBlock } from "microapps";

interface MovistarInfoProps {
  characterName: string;
  isVisible: boolean;
  onClose: () => void;
}

/**
 * Component to display Movistar+ content information for a character
 *
 * Features:
 * - Searches for content by actor name and title
 * - Modal-style display with overlay
 * - Loading states and error handling
 * - Responsive design with tabs for different search types
 */
export const MovistarInfo: React.FC<MovistarInfoProps> = ({
  characterName,
  isVisible,
  onClose,
}) => {
  const [originalResult, setOriginalResult] = useState<string>("");
  const [normalizedResult, setNormalizedResult] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [useNormalizedFormat, setUseNormalizedFormat] = useState<boolean>(true);

  const movistarService = new MovistarOttService();

  /**
   * Reset state when component becomes visible
   */
  useEffect(() => {
    if (isVisible && characterName) {
      fetchMovistarInfo();
    }
  }, [isVisible, characterName]);

  /**
   * Normalize the format of Movistar+ response to ensure consistent display
   */
  const normalizeMovistarResponse = (response: string): string => {
    // Check if response uses numbered list format (problematic format)
    const hasNumberedList = /^\d+\.\s+\*\*/.test(response.trim());

    if (hasNumberedList) {
      // Split response into sections by numbered items
      const sections = response.split(/(?=^\d+\.\s+\*\*)/gm).filter(section => section.trim());

      const normalizedSections = sections.map(section => {
        // Extract title from "1. **Title**"
        const titleMatch = section.match(/^\d+\.\s+\*\*([^*]+)\*\*/);
        if (!titleMatch) return section;

        const title = titleMatch[1].trim();
        let content = section.replace(/^\d+\.\s+\*\*[^*]+\*\*\s*/, '');

        // Extract image if it exists at the end
        const imageMatch = content.match(/!\[([^\]]*)\]\(([^)]+)\)\s*$/);
        let imageMarkdown = '';
        if (imageMatch) {
          imageMarkdown = `\n![${imageMatch[1]}](${imageMatch[2]})\n`;
          content = content.replace(/\s*!\[([^\]]*)\]\(([^)]+)\)\s*$/, '');
        }

        // Format the section with header style
        return `### ${title}${imageMarkdown}${content.trim()}`;
      });

      return normalizedSections.join('\n\n');
    }

    return response;
  };

  /**
   * Fetch Movistar+ information for the character using OTT service
   */
  const fetchMovistarInfo = async () => {
    if (!characterName.trim()) return;

    setLoading(true);
    setError("");
    setOriginalResult("");
    setNormalizedResult("");

    try {
      // console.log(`🎬 Buscando contenido de Movistar+ para: ${characterName}`);

      // Inicializar el servicio
      await movistarService.init();

      // Enviar consulta al agente
      const result = await movistarService.sendToAgentFinalOnly(
        // `Tan solo quiero una pelicula del personaje ${characterName}`
        `Dame películas y series del personaje ${characterName}`
      );
      console.log("✅ Resultado del agente Movistar+:", result);

      // Store both original and normalized results
      const normalized = normalizeMovistarResponse(result);
      console.log("🔧 Resultado normalizado:", normalized);

      setOriginalResult(result);
      setNormalizedResult(normalized);

      // console.log("✅ Búsqueda de Movistar+ completada");
    } catch (err) {
      console.error("❌ Error fetching Movistar+ info:", err);
      setError(
        err instanceof Error
          ? err.message
          : "Error desconocido al obtener información de Movistar+"
      );
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle retry action
   */
  const handleRetry = () => {
    setOriginalResult("");
    setNormalizedResult("");
    setError("");
    fetchMovistarInfo();
  };

  /**
   * Get the current result to display based on format preference
   */
  const getCurrentResult = () => {
    return useNormalizedFormat ? normalizedResult : originalResult;
  };

  if (!isVisible) return null;

  return (
    <>
      <div
        style={{
          position: "fixed",
          top: 0,
          width: "100%",
          height: "100%",
          background: "rgba(8, 31, 46, 0.8)",
          zIndex: 1000,
        }}
        onClick={onClose}
      >
        <div
          style={{
            padding: "24px",
            height: "100%",
            display: "flex",
            flexDirection: "column",
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "20px",
              borderBottom: "1px solid #eee",
              paddingBottom: "15px",
            }}
          >
            <h3 style={{ margin: 0, color: "#fff" }}>
              Contenido Movistar+ - {characterName}
            </h3>

            <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
              <IconButton
                ariaLabel="Cerrar"
                iconType="close"
                onClick={onClose}
                size="small"
                state="default"
                backgroundColor="transparent"
              />
            </div>
          </div>

          <div
            className="movistar-films"
            style={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              minHeight: 0,
            }}
          >
            {loading && (
              <LoadBlock
                interval={3000}
                text="Buscando contenido en Movistar+..."
              />
            )}

            {!loading && !error && getCurrentResult() && (
              <>
                <div
                  style={{
                    padding: "15px",
                    flex: 1,
                    overflowY: "auto",
                    fontFamily: "inherit",
                    fontSize: "14px",
                    lineHeight: "1.5",
                    color: "#fff",
                  }}
                >
                  <ReactMarkdown
                    components={{
                      h3: ({ children }) => (
                        <h3
                          style={{
                            color: "#88FFD5",
                            margin: "16px 0 10px 0",
                            fontSize: "3rem",
                            fontWeight: "bold",
                            textAlign: "center",
                          }}
                        >
                          {children}
                        </h3>
                      ),
                      img: ({ src, alt }) => (
                        <Image
                          src={src}
                          alt={alt || "Imagen"}
                          aspectRatio="7:10"
                          objectFit="cover"
                          width="300px"
                          className="movistar-plus-image"
                        />
                      ),
                      a: ({ href, children }) => (
                        <a
                          href={href}
                          target="_blank"
                          rel="noopener noreferrer"
                          style={{ color: "#88FFD5", textDecoration: "none" }}
                        >
                          {children}
                        </a>
                      ),
                      strong: ({ children }) => (
                        <strong style={{ color: "#88FFD5", margin: "1rem 0" }}>{children}</strong>
                      ),
                      ol: ({ children }) => (
                        <ol style={{ margin: "8px 0" }} className="markdown-ol">
                          {children}
                        </ol>
                      ),
                      ul: ({ children }) => (
                        <ul style={{ margin: "8px 0", paddingLeft: "20px" }} className="markdown-ul">
                          {children}
                        </ul>
                      ),
                      li: ({ children }) => (
                        <li className="markdown-li">{children}</li>
                      ),
                    }}
                  >
                    {getCurrentResult()}
                  </ReactMarkdown>
                </div>
              </>
            )}

            {!loading && !error && !getCurrentResult() && (
              <div
                style={{
                  textAlign: "center",
                  padding: "40px 20px",
                  color: "#666",
                }}
              >
                <p style={{ marginBottom: "20px" }}>
                  No se encontraron resultados para "{characterName}"
                </p>
                <button
                  onClick={handleRetry}
                  style={{
                    backgroundColor: "#007bff",
                    color: "white",
                    border: "none",
                    padding: "10px 20px",
                    borderRadius: "5px",
                    cursor: "pointer",
                    fontSize: "14px",
                  }}
                >
                  🔄 Buscar de nuevo
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
