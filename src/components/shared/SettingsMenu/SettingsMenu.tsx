// React core
import { useState, useEffect } from "react";
// Third-party library imports
import { Modal } from "microapps";
// Utils & Constants & Helpers
import { COOKIE_CONSENT_KEY } from "../../../utils/cookieUtils";
// Styles
import "./SettingsMenu.scss";

interface SettingsMenuProps {
  isVisible: boolean;
  onClose: () => void;
  onBackToMain: () => void;
  onMusicToggle: () => void;
  onSpeechToggle: () => void;
  isMusicPlaying: boolean;
  isSpeechEnabled: boolean;
}

export const SettingsMenu: React.FC<SettingsMenuProps> = ({
  isVisible,
  onClose,
  onBackToMain,
  onMusicToggle,
  onSpeechToggle,
  isMusicPlaying,
  isSpeechEnabled,
}) => {
  const [googleAnalyticsEnabled, setGoogleAnalyticsEnabled] = useState(false);

  // Load initial Google Analytics consent state
  useEffect(() => {
    try {
      const consent = localStorage.getItem(COOKIE_CONSENT_KEY);
      setGoogleAnalyticsEnabled(consent === "accepted");
    } catch (error) {
      console.warn("⚠️ Error loading Google Analytics consent:", error);
      setGoogleAnalyticsEnabled(false);
    }
  }, []);

  const handleBackToMain = () => {
    onBackToMain();
    onClose();
  };

  const handleMusicToggle = () => {
    onMusicToggle();
  };

  const handleSpeechToggle = () => {
    onSpeechToggle();
  };

  const handleGoogleAnalyticsToggle = () => {
    try {
      const newValue = !googleAnalyticsEnabled;
      const consentValue = newValue ? "accepted" : "not accepted";
      localStorage.setItem(COOKIE_CONSENT_KEY, consentValue);
      setGoogleAnalyticsEnabled(newValue);
    } catch (error) {
      console.error("❌ Error updating Google Analytics consent:", error);
    }
  };

  const handleAboutGame = () => {
    // No hacer nada según las especificaciones
    console.log("Sobre el juego - No action required");
  };

  const renderBody = () => {
    return (
      <div className="settings-menu-body">
        <div className="settings-item" onClick={handleBackToMain}>
          <span className="settings-label">Volver al inicio</span>
        </div>

        <div className="settings-item" onClick={handleMusicToggle}>
          <span className="settings-label">Música de fondo</span>
          <div
            className={`settings-toggle ${
              isMusicPlaying ? "enabled" : "disabled"
            }`}
          >
            <div className="toggle-slider"></div>
          </div>
        </div>

        <div className="settings-item" onClick={handleSpeechToggle}>
          <span className="settings-label">Leer en voz alta</span>
          <div
            className={`settings-toggle ${
              isSpeechEnabled ? "enabled" : "disabled"
            }`}
          >
            <div className="toggle-slider"></div>
          </div>
        </div>

        <div className="settings-item" onClick={handleGoogleAnalyticsToggle}>
          <span className="settings-label">
            Permitir envío de estadísticas a Google Analytics
          </span>
          <div
            className={`settings-toggle ${
              googleAnalyticsEnabled ? "enabled" : "disabled"
            }`}
          >
            <div className="toggle-slider"></div>
          </div>
        </div>

        <div className="settings-item" onClick={handleAboutGame}>
          <span className="settings-label">Sobre el juego</span>
        </div>
      </div>
    );
  };

  if (!isVisible) return null;

  return <Modal title="" onClose={onClose} body={renderBody()} />;
};
