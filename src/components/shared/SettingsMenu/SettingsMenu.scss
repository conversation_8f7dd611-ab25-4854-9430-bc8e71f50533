.settings-menu-body {
  padding: 0;
  margin: 0;

  .settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background-color: #1a3a52;
    border-bottom: 1px solid #2a4a62;
    cursor: pointer;
    transition: background-color 0.2s ease;
    color: white;
    font-size: 1rem;
    font-weight: 400;

    &:first-child {
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;
    }

    &:last-child {
      border-bottom: none;
      border-bottom-left-radius: 12px;
      border-bottom-right-radius: 12px;
    }

    &:hover {
      background-color: #2a4a62;
    }

    .settings-label {
      flex: 1;
      text-align: left;
    }

    .settings-toggle {
      width: 50px;
      height: 26px;
      background-color: #4a6a82;
      border-radius: 13px;
      position: relative;
      transition: background-color 0.3s ease;
      cursor: pointer;

      &.enabled {
        background-color: #88ffd5;
      }

      .toggle-slider {
        width: 22px;
        height: 22px;
        background-color: white;
        border-radius: 50%;
        position: absolute;
        top: 2px;
        left: 2px;
        transition: transform 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      &.enabled .toggle-slider {
        transform: translateX(24px);
      }
    }
  }
}

// Override modal styles for settings menu
:global(.modal-container) {
  .modal-content {
    .settings-menu-body {
      margin: -24px;
      border-radius: 12px;
      overflow: hidden;
    }
  }
}

// Alternative approach - target the modal directly
:global(.modal) {
  .settings-menu-body {
    margin: -24px;
    border-radius: 12px;
    overflow: hidden;
  }
}
