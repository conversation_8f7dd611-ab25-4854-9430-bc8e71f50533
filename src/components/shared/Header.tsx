// React core
import { useEffect, useState } from "react";
// Third-party library imports
import { ControlButton, NavButton, SafeAreaHome } from "microapps";
// Services
import { audioStateManager } from "../../services/AudioStateManager";
import { VoicesService } from "../../services/VoicesService";
// Components
import { SettingsMenu } from "./SettingsMenu";
// Utils & Constants & Helpers
import { startBackgroundMusic, stopBackgroundMusic } from "../../utils/audioUtils";
import { GameStep, type GameStepType } from "../../utils/gameUtils";
// Styles
import "./Header.scss";

interface HeaderProps {
  currentStep: GameStepType;
  onBackToMain?: () => void;
  onBackToMainMenu?: () => void;
  showBackButton?: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  currentStep,
  onBackToMain,
  onBackToMainMenu,
  showBackButton = false,
}) => {
  // Estados para controlar música y speech
  const [isMusicPlaying, setIsMusicPlaying] = useState(true);
  const [currentAzureTTSState, setCurrentAzureTTSState] = useState(true);
  const [audioState, setAudioState] = useState(audioStateManager.getState());
  const [isSettingsMenuVisible, setIsSettingsMenuVisible] = useState(false);
  const voicesService = VoicesService.getInstance();

  /**
   * Efecto para monitorear cambios en el estado del audio y sincronizar TTS
   */
  useEffect(() => {
    const updateAudioState = () => {
      const newState = audioStateManager.getState();
      setAudioState(newState);
      setIsMusicPlaying(newState.isBackgroundMusicPlaying);
    };

    // Sincronizar estado inicial del TTS con el servicio de voces
    const syncTTSState = () => {
      const isVoiceConfigured = voicesService.isVoiceConfigured();
      setCurrentAzureTTSState(isVoiceConfigured);
    };

    // Actualizar estados iniciales
    updateAudioState();
    syncTTSState();

    // Polling para monitorear cambios (ya que subscribe está comentado)
    const interval = setInterval(() => {
      updateAudioState();
      syncTTSState();
    }, 1000);

    return () => clearInterval(interval);
  }, [voicesService]);

  /**
   * Manejar click en el botón de música
   */
  const handleMusicClick = async () => {
    if (audioState.isBackgroundMusicPlaying) {
      // Detener música
      stopBackgroundMusic();
      setIsMusicPlaying(false);
      // console.log("🔇 Música de fondo detenida");
    } else {
      // Iniciar música
      try {
        await startBackgroundMusic("/assets/sounds/sound.mp3");
        setIsMusicPlaying(true);
        // console.log("🎵 Música de fondo iniciada");
      } catch (error) {
        console.warn("⚠️ Error iniciando música de fondo:", error);
        setIsMusicPlaying(false);
      }
    }
  };

  /**
   * Manejar click en el botón de speech/TTS
   */
  const handleSpeechClick = async () => {
    if (currentAzureTTSState) {
      // Desactivar TTS
      voicesService.disableVoice();
      setCurrentAzureTTSState(false);
      // console.log("🔇 Servicio de TTS desactivado");
    } else {
      // Activar TTS
      try {
        const isConfigured = await voicesService.enableVoice();
        if (isConfigured) {
          setCurrentAzureTTSState(true);
          // console.log("🔊 Servicio de TTS activado");
        } else {
          console.warn("⚠️ No se pudo configurar el servicio de TTS");
          setCurrentAzureTTSState(false);
        }
      } catch (error) {
        console.error("❌ Error configurando TTS:", error);
        setCurrentAzureTTSState(false);
      }
    }
  };

  /**
   * Manejar click en el botón de menú
   */
  const handleMenuClick = () => {
    setIsSettingsMenuVisible(true);
  };

  /**
   * Manejar cierre del menú de configuración
   */
  const handleCloseSettingsMenu = () => {
    setIsSettingsMenuVisible(false);
  };

  /**
   * Manejar navegación al menú principal desde el menú de configuración
   */
  const handleBackToMainMenu = () => {
    if (onBackToMainMenu) {
      onBackToMainMenu();
    }
  };

  const renderTitle = () => {
    switch (currentStep) {
      case GameStep.GAME_RULES:
        return "";
      case GameStep.GAME_PLAYING:
        return "Enygma";
      case GameStep.GAME_HINT:
        return "Pistas descubiertas";
    }
  };

  return (
    <>
      <div className="header">
        <div className="header-left">
          {currentStep === GameStep.MAIN_MENU && (
            <NavButton type="menu" onClick={handleMenuClick} size="big" />
          )}

          {showBackButton && currentStep !== GameStep.MAIN_MENU && onBackToMain && (
            <NavButton type="back" onClick={onBackToMain} size="big" />
          )}
        </div>

        <div className="header-title">{renderTitle()}</div>

        <div className="header-right">
          <ControlButton
            onClick={handleMusicClick}
            type="music"
            isActive={isMusicPlaying && audioState.isBackgroundMusicPlaying}
            size="big"
          />

          <ControlButton
            type="sound"
            isActive={currentAzureTTSState}
            size="big"
            onClick={handleSpeechClick}
            className={`speech-control`}
          />

          <div className="home-icon">
            <SafeAreaHome isVisible={false} />
          </div>
        </div>
      </div>

      <SettingsMenu
        isVisible={isSettingsMenuVisible}
        onClose={handleCloseSettingsMenu}
        onBackToMain={handleBackToMainMenu}
        onMusicToggle={handleMusicClick}
        onSpeechToggle={handleSpeechClick}
        isMusicPlaying={isMusicPlaying}
        isSpeechEnabled={currentAzureTTSState}
      />
    </>
  );
};
