import React, { useState, useEffect } from "react";
import ReactMarkdown from "react-markdown";
import { PerplexityService } from "../services/PerplexityService";
import type { PerplexityGenerateResponse } from "../types/PerplexityTypes";
import { LoadBlock, Modal } from "microapps";

interface CharacterInfoProps {
  characterName: string;
  isVisible: boolean;
  onClose: () => void;
}

export const CharacterInfo: React.FC<CharacterInfoProps> = ({
  characterName,
  isVisible,
  onClose,
}) => {
  const [characterInfo, setCharacterInfo] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");

  const perplexityService = PerplexityService.getInstance();

  /**
   * Fetch character information when component becomes visible
   */
  useEffect(() => {
    if (isVisible && characterName && !characterInfo) {
      fetchCharacterInfo();
    }
  }, [isVisible, characterName]);

  /**
   * Fetch character information from Perplexity
   */
  const fetchCharacterInfo = async () => {
    if (!characterName.trim()) return;

    setLoading(true);
    setError("");

    try {
      const response: PerplexityGenerateResponse =
        await perplexityService.getCharacterInfo(characterName);

      if (response.ok && response.output) {
        setCharacterInfo(response.output);
      } else {
        throw new Error(
          response.message || "No se pudo obtener información del personaje"
        );
      }
    } catch (err) {
      console.error("❌ Error fetching character info:", err);
      setError(
        err instanceof Error
          ? err.message
          : "Error desconocido al obtener información"
      );
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle close action
   */
  const handleClose = () => {
    setCharacterInfo("");
    setError("");
    onClose();
  };

  // Detectar si el contenido tiene formato Markdown
  const hasMarkdown =
    characterInfo &&
    (characterInfo.includes("#") ||
      characterInfo.includes("**") ||
      characterInfo.includes("*") ||
      characterInfo.includes("\n-") ||
      characterInfo.includes("\n1.") ||
      characterInfo.includes("###"));

  const bodyHtml = () => {
    return (
      <>
        {loading && (
          <LoadBlock
            interval={3000}
            text={`Buscando información sobre ${characterName}..`}
          />
        )}

        {characterInfo && !loading && !error && (
          <div>
            {hasMarkdown ? (
              <ReactMarkdown
                components={{
                  h3: ({ children }) => (
                    <h3
                      style={{
                        fontSize: "1.3rem",
                        fontWeight: "600",
                        marginBottom: "1rem",
                        marginTop: "2rem",
                      }}
                    >
                      {children}
                    </h3>
                  ),
                }}
              >
                {characterInfo}
              </ReactMarkdown>
            ) : (
              <div
                style={{
                  whiteSpace: "pre-wrap",
                  fontSize: "15px",
                }}
              >
                {characterInfo}
              </div>
            )}
          </div>
        )}
      </>
    );
  };

  if (!isVisible) return null;

  return (
    <>
      <Modal
        onClose={handleClose}
        title={`Información del Personaje ${characterName}`}
        body={bodyHtml()}
      />
    </>
  );
};
