@font-face {
  font-family: "OnAir";
  src: url("/assets/fonts/OnAir-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "OnAir";
  src: url("/assets/fonts/OnAir-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: "OnAir";
  src: url("/assets/fonts/OnAir-Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: "OnAir";
  src: url("/assets/fonts/OnAir-LightItalic.ttf") format("truetype");
  font-weight: 400;
  font-style: italic;
}

@font-face {
  font-family: "OnAir";
  src: url("/assets/fonts/OnAir-Light.ttf") format("truetype");
  font-weight: 400;
}

@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap");

/* Global background styling */
* {
  color: white;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-image: url("/assets/game/background.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

#root {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: transparent;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
  color: #88ffd5;
}

/* Estilos específicos para listas en markdown */
.markdown-ol {
  margin: 8px 0;
  padding-left: 20px;
}

.markdown-ul {
  .markdown-li {
    margin-bottom: 1rem;
    list-style-type: none;
    color: white;
    border: none;
  }
}

/* Estilos para li dentro de ol (listas ordenadas) */
.markdown-ol .markdown-li {
  // margin-bottom: 2rem;
  // border-bottom: 1px solid #eee;
  // padding-bottom: 1rem;
  list-style-type: decimal;
  color: white;
}
